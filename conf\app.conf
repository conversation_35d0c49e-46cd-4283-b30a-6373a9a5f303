# 微信API服务 - 统一配置文件
# 支持环境变量覆盖，格式：${ENV_VAR_NAME||default_value}

# ==================== 应用基本配置 ====================
appname = wechat
httpaddr = "0.0.0.0"
httpport = 8059
wsport = 8088
runmode = prod

# ==================== 演示站配置 ====================
# 演示站模式 - 启用后所有配置项将变为只读状态，防止在演示环境中被误修改
demo_mode = flase

# 管理员密钥 - 用于在演示模式下禁用演示模式的特殊密钥
admin_key = "admin_kedaya_2024"

# ==================== 部署环境配置 ====================
# 部署模式：local(本地开发) | http(HTTP服务) | tcp(TCP服务) | docker(容器)
deploy_mode = "local"

# ==================== Redis配置 ====================
# Redis连接配置（必需）
redislink = 189.1.234.127:6379
redispass = "123456aa"
redisdbnum = 2

# Redis连接池优化配置
redis_pool_size = 100                   # 连接池大小（增加到100）
redis_min_idle_conns = 20               # 最小空闲连接数（增加到20）
redis_max_retries = 3                   # 最大重试次数（减少到3）
redis_dial_timeout = 5                  # 连接超时（秒，减少到5秒）
redis_read_timeout = 3                  # 读取超时（秒，减少到3秒）
redis_write_timeout = 3                 # 写入超时（秒，减少到3秒）
redis_idle_timeout = 300                # 空闲连接超时（秒，减少到5分钟）
redis_pool_timeout = 5                  # 连接池获取连接超时（秒，减少到5秒）

# ==================== 长连接配置 ====================
# 长连接启用状态 - 启用后支持WebSocket和TCP长连接
longlinkenabled = true
# 长连接超时时间（秒）
longlinkconnecttimeout = "10m"
# 是否启用WebSocket测试消息（每5秒发送一次测试消息）
ws_enable_test_messages = false
# 是否启用WebSocket自动消息同步（每10秒同步一次，备选方案）
ws_enable_auto_sync = true

# ==================== 外部服务配置 ====================
ocrurl = "http://150.158.2.208:9050/api/UnBan/WXSliderOCR"
syncmessagebusinessuri = ""

# ==================== API安全配置 ====================
# 统一的API密钥配置（用于API接口和文档访问）
api_key = "api_kedaya"
api_key_enabled = false

# ==================== API文档配置 ====================
api_docs_route = "/"

# ==================== 环境特定配置 ====================
# 根据deploy_mode自动调整的配置项

# HTTP模式特定配置 (deploy_mode = "http")
[http]
httpport = 8005
redisdbnum = 1
syncmessagebusinessuri = "http://150.158.2.208:9100/wic/wechat/{0}/SyncMessage"

# TCP模式特定配置 (deploy_mode = "tcp")
[tcp]
httpport = 8057
redisdbnum = 7
syncmessagebusinessuri = "http://150.158.2.208:9100/wic/wechat/{0}/SyncMessage"
