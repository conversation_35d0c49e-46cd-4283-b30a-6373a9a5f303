// @APIVersion
// @Title Wechat 08- 算法
// @Description 可开启自动心跳 - 自动二次登录 - 长连接心跳  （新上线账号尽量 ipad、安卓 pad 加好友发圈。IOS 参数比较全）
package routers

import (
	"wechatdll/comm"
	"wechatdll/controllers"
	"wechatdll/middleware"

	"github.com/astaxie/beego"
	"github.com/astaxie/beego/plugins/cors"
	log "github.com/sirupsen/logrus"
)

func init() {
	// 注册API文档相关路由（支持自定义路由和密钥）
	registerAPIDocsRoutes()

	// 添加中间件
	beego.InsertFilter("*", beego.BeforeRouter, cors.Allow(&cors.Options{
		AllowAllOrigins:  true,
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Authorization", "Access-Control-Allow-Origin", "Access-Control-Allow-Headers", "Content-Type"},
		ExposeHeaders:    []string{"Content-Length", "Access-Control-Allow-Origin", "Access-Control-Allow-Headers", "Content-Type"},
		AllowCredentials: true,
	}))

	// 添加频率限制中间件（已禁用）
	// beego.InsertFilter("/api/*", beego.BeforeRouter, middleware.RateLimitMiddleware)

	// 为敏感接口添加API密钥验证
	beego.InsertFilter("/api/Login/GetLoggedAccounts", beego.BeforeRouter, middleware.APIKeyAuthMiddleware)
	beego.InsertFilter("/api/Login/DeleteAccount", beego.BeforeRouter, middleware.APIKeyAuthMiddleware)
	beego.InsertFilter("/api/Config/*", beego.BeforeRouter, middleware.APIKeyAuthMiddleware)

	// 手动注册登录管理路由
	beego.Router("/api/Login/DeleteAccount", &controllers.LoginController{}, "post:DeleteAccount")

	// 手动注册配置管理路由
	beego.Router("/api/Config/GetConfigs", &controllers.ConfigController{}, "get:GetConfigs")
	beego.Router("/api/Config/UpdateConfig", &controllers.ConfigController{}, "post:UpdateConfig")
	beego.Router("/api/Config/GetConfigFile", &controllers.ConfigController{}, "get:GetConfigFile")

	// 注册API文档配置管理路由
	beego.Router("/api/APIDocsConfig/GetAPIDocsConfig", &controllers.APIDocsConfigController{}, "get:GetAPIDocsConfig")
	beego.Router("/api/APIDocsConfig/UpdateAPIDocsConfig", &controllers.APIDocsConfigController{}, "post:UpdateAPIDocsConfig")
	beego.Router("/api/APIDocsConfig/GetCurrentAPIDocsURL", &controllers.APIDocsConfigController{}, "get:GetCurrentAPIDocsURL")
	beego.Router("/api/APIDocsConfig/ValidateAPIDocsConfig", &controllers.APIDocsConfigController{}, "post:ValidateAPIDocsConfig")

	// 注册消息类型管理路由
	beego.Router("/api/MessageType/types", &controllers.MessageTypeController{}, "get:GetAllMessageTypes")
	beego.Router("/api/MessageType/types/category", &controllers.MessageTypeController{}, "get:GetMessageTypesByCategory")
	beego.Router("/api/MessageType/parse", &controllers.MessageTypeController{}, "post:ParseMessage")
	beego.Router("/api/MessageType/stats", &controllers.MessageTypeController{}, "get:GetMessageTypeStats")
	beego.Router("/api/MessageType/check", &controllers.MessageTypeController{}, "get:CheckMessageType")

	// 手动注册WebSocket相关路由
	beego.Router("/api/msg/TestWebSocket", &controllers.MsgController{}, "get:TestWebSocket")
	beego.Router("/api/msg/SyncAndPush", &controllers.MsgController{}, "get:SyncAndPush")
	beego.Router("/api/msg/WebSocketStatus", &controllers.MsgController{}, "get:WebSocketStatus")
	beego.Router("/api/msg/TestMsgListener", &controllers.MsgController{}, "get:TestMsgListener")
	beego.Router("/api/msg/AutoRedPacket", &controllers.MsgController{}, "post:AutoRedPacket")
	beego.Router("/api/msg/GetAutoRedPacketStatus", &controllers.MsgController{}, "get:GetAutoRedPacketStatus")

	// 手动注册TenPay相关路由（如果自动生成的路由有问题）
	beego.Router("/api/TenPay/GetEncryptInfo", &controllers.TenPayController{}, "post:GetEncryptInfo")
	beego.Router("/api/TenPay/Receivewxhb", &controllers.TenPayController{}, "post:Receivewxhb")
	beego.Router("/api/TenPay/Openwxhb", &controllers.TenPayController{}, "post:Openwxhb")

	// 手动注册通讯录缓存相关路由
	beego.Router("/api/Friend/ClearContactCache", &controllers.FriendController{}, "post:ClearContactCache")
	beego.Router("/api/Friend/GetContactCacheStats", &controllers.FriendController{}, "get:GetContactCacheStats")

	// 自动红包领取相关路由
	beego.Router("/api/TenPay/AutoRedPacketSetting", &controllers.TenPayController{}, "post:AutoRedPacketSetting")
	beego.Router("/api/TenPay/GetAutoRedPacketSetting", &controllers.TenPayController{}, "get:GetAutoRedPacketSetting")

	// 自动收款确认相关路由
	beego.Router("/api/TenPay/AutoCollectMoneySetting", &controllers.TenPayController{}, "post:AutoCollectMoneySetting")
	beego.Router("/api/TenPay/GetAutoCollectMoneySetting", &controllers.TenPayController{}, "get:GetAutoCollectMoneySetting")

	// 代理管理相关路由
	beego.Router("/api/Proxy/ImportProxies", &controllers.ProxyController{}, "post:ImportProxies")
	beego.Router("/api/Proxy/GetProxyList", &controllers.ProxyController{}, "get:GetProxyList")
	beego.Router("/api/Proxy/GetProxyStats", &controllers.ProxyController{}, "get:GetProxyStats")
	beego.Router("/api/Proxy/TestProxies", &controllers.ProxyController{}, "post:TestProxies")
	beego.Router("/api/Proxy/DeleteProxy/*", &controllers.ProxyController{}, "delete:DeleteProxy")
	beego.Router("/api/Proxy/ClearProxies", &controllers.ProxyController{}, "post:ClearProxies")

	// 注册API密钥验证相关路由（这些路由不需要密钥验证）
	beego.Router("/api/auth", &controllers.APIAuthController{}, "get:AuthPage")
	beego.Router("/api/auth/verify", &controllers.APIAuthController{}, "post:VerifyKey")

	ns := beego.NewNamespace("/api",
		beego.NSNamespace("/Login",
			beego.NSInclude(
				&controllers.LoginController{},
			),
		),

		beego.NSNamespace("/Msg",
			beego.NSInclude(
				&controllers.MsgController{},
			),
		),

		beego.NSNamespace("/Friend",
			beego.NSInclude(
				&controllers.FriendController{},
			),
		),
		beego.NSNamespace("/Finder",
			beego.NSInclude(
				&controllers.FinderController{},
			),
		),
		beego.NSNamespace("/FriendCircle",
			beego.NSInclude(
				&controllers.FriendCircleController{},
			),
		),
		beego.NSNamespace("/Favor",
			beego.NSInclude(
				&controllers.FavorController{},
			),
		),
		beego.NSNamespace("/Group",
			beego.NSInclude(
				&controllers.GroupController{},
			),
		),
		beego.NSNamespace("/Label",
			beego.NSInclude(
				&controllers.LabelController{},
			),
		),
		beego.NSNamespace("/User",
			beego.NSInclude(
				&controllers.UserController{},
			),
		),
		beego.NSNamespace("/Wxapp",
			beego.NSInclude(
				&controllers.WxappController{},
			),
		),
		beego.NSNamespace("/QWContact",
			beego.NSInclude(
				&controllers.QWContactController{},
			),
		),
		beego.NSNamespace("/OfficialAccounts",
			beego.NSInclude(
				&controllers.OfficialAccountsController{},
			),
		),
		beego.NSNamespace("/SayHello",
			beego.NSInclude(
				&controllers.SayHelloController{},
			),
		),
		beego.NSNamespace("/Tools",
			beego.NSInclude(
				&controllers.ToolsController{},
			),
		),
		beego.NSNamespace("/TenPay",
			beego.NSInclude(
				&controllers.TenPayController{},
			),
		),
		beego.NSNamespace("/Monitor",
			beego.NSInclude(
				&controllers.MonitorController{},
			),
		),
		beego.NSNamespace("/Config",
			beego.NSInclude(
				&controllers.ConfigController{},
			),
		),
		beego.NSNamespace("/Proxy",
			beego.NSInclude(
				&controllers.ProxyController{},
			),
		),
		beego.NSNamespace("/system",
			beego.NSRouter("/info", &controllers.MonitorController{}, "get:GetSystemInfo"),
		),
	)
	beego.AddNamespace(ns)
}

// registerAPIDocsRoutes 注册API文档相关路由
func registerAPIDocsRoutes() {
	config := comm.GetAPIDocsConfig()

	// 注册主API文档路由
	beego.Router(config.Route, &controllers.APIController{})

	// 注册swagger.json路由
	beego.Router("/swagger.json", &controllers.APIJSONController{})

	// 配置静态文件服务 - 直接服务静态文件，不通过控制器
	setupStaticFileService()

	// 如果启用了密钥验证，添加中间件
	if config.KeyEnabled {
		// 创建自定义的API文档认证中间件
		apiDocAuthMiddleware := middleware.CreateAPIDocAuthMiddleware(config.Key)

		// 为API文档相关路由添加密钥验证中间件
		// 注意：不对 /api/auth 和 /api/auth/verify 路由添加验证
		beego.InsertFilter(config.Route, beego.BeforeRouter, apiDocAuthMiddleware)
		beego.InsertFilter("/swagger.json", beego.BeforeRouter, apiDocAuthMiddleware)
		beego.InsertFilter("/styles/*", beego.BeforeRouter, apiDocAuthMiddleware)
		beego.InsertFilter("/js/*", beego.BeforeRouter, apiDocAuthMiddleware)

		log.WithField("route", config.Route).Info("API文档密钥验证已启用")
	} else {
		log.WithField("route", config.Route).Warn("API文档密钥验证已禁用")
	}
}

// setupStaticFileService 配置静态文件服务
func setupStaticFileService() {
	// 配置静态文件路径映射
	// /styles/* -> docs/styles/
	// /js/* -> docs/js/
	beego.SetStaticPath("/styles", "docs/styles")
	beego.SetStaticPath("/js", "docs/js")

}
