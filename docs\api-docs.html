<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WeChat API 文档</title>

    <!-- 主题预加载脚本 - 防止主题闪烁 -->
    <script>
        (function() {
            // 立即读取并应用存储的主题，防止闪烁
            try {
                const storedTheme = localStorage.getItem('theme');
                const theme = storedTheme || 'light'; // 默认为亮色主题

                if (theme === 'dark') {
                    document.documentElement.setAttribute('data-theme', 'dark');
                } else {
                    document.documentElement.removeAttribute('data-theme');
                }

                console.log('[ThemePreloader] 预加载主题:', theme);
            } catch (error) {
                console.warn('[ThemePreloader] 主题预加载失败:', error);
                // 出错时默认使用亮色主题
                document.documentElement.removeAttribute('data-theme');
            }
        })();
    </script>

    <link rel="stylesheet" href="styles/main.css?v=2.3">
    <link rel="stylesheet" href="styles/buttons.css?v=1.0">
    <link rel="stylesheet" href="styles/friends.css?v=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>

<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="header-container">
            <div class="header-left">
                <button class="mobile-menu-btn" id="mobile-menu-btn">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <i class="fab fa-weixin"></i>
                    <span>WeChat API</span>
                </div>
                <nav class="nav-menu">
                    <a href="#overview" class="nav-item active">概览</a>
                    <a href="#friends" class="nav-item">好友管理</a>
                    <a href="#config" class="nav-item">修改配置项</a>
                </nav>
            </div>
            <div class="header-right">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="搜索接口..." id="global-search">
                </div>
                <div class="theme-toggle">
                    <button class="theme-toggle-btn" id="theme-toggle-btn" aria-label="切换主题模式" title="切换明亮/暗黑主题">
                        <i class="fas fa-moon theme-icon-dark"></i>
                        <i class="fas fa-sun theme-icon-light"></i>
                    </button>
                </div>
                <div class="config-panel">
                    <button class="config-btn" id="config-btn">
                        <i class="fas fa-cog"></i>
                        配置
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- 配置面板 -->
    <div class="config-modal" id="config-modal">
        <div class="config-content">
            <div class="config-header">
                <h3>API 配置</h3>
                <button class="close-btn" id="close-config">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="config-body">
                <div class="config-item">
                    <label>默认 Wxid</label>
                    <input type="text" id="default-wxid" placeholder="请输入登录成功的 wxid">
                    <small>设置后将自动填充到所有需要 wxid 的接口中</small>
                </div>
                <div class="config-item">
                    <label>
                        <input type="checkbox" id="debug-mode-toggle">
                        开发调试模式
                    </label>
                    <small>开启后将在控制台显示详细的调试信息，用于开发和问题排查</small>
                </div>
                <div class="config-item">
                    <label>API 基础地址</label>
                    <input type="text" id="base-url" placeholder="http://localhost:8059/api">
                </div>
                <div class="config-item">
                    <label>请求超时时间</label>
                    <select id="timeout">
                        <option value="5000">5秒</option>
                        <option value="10000" selected>10秒</option>
                        <option value="30000">30秒</option>
                    </select>
                </div>
            </div>
            <div class="config-footer">
                <button class="btn btn-primary" id="save-config">保存配置</button>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 侧边栏 -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-title">
                    <h3>导航菜单</h3>
                    <button class="sidebar-toggle" id="sidebar-toggle">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                </div>
            </div>
            <div class="sidebar-content">
                <!-- 移动端页面导航 -->
                <div class="mobile-nav-menu" id="mobile-nav-menu">
                    <a href="#overview" class="mobile-nav-item active" data-section="overview">
                        <i class="fas fa-home"></i>
                        <span>概览</span>
                    </a>
                    <a href="#friends" class="mobile-nav-item" data-section="friends">
                        <i class="fas fa-user-friends"></i>
                        <span>好友管理</span>
                    </a>
                    <a href="#config" class="mobile-nav-item" data-section="config">
                        <i class="fas fa-cog"></i>
                        <span>修改配置项</span>
                    </a>
                </div>

                <!-- 分割线 -->
                <div class="sidebar-divider">
                    <span>接口分类</span>
                </div>



                <!-- 接口分类列表 -->
                <div class="category-list" id="category-list">
                    <!-- 动态生成分类列表 -->
                </div>
            </div>
        </aside>

        <!-- 侧边栏遮罩 (移动端) -->
        <div class="sidebar-overlay" id="sidebar-overlay"></div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 概览页面 -->
            <section id="overview-section" class="content-section active">
                <div class="overview-cards">
                    <div class="overview-card">
                        <div class="card-icon">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <div class="card-content">
                            <h3>快速开始</h3>
                            <p>支持自动心跳、自动二次登录、长连接心跳</p>
                        </div>
                    </div>
                    <div class="overview-card">
                        <div class="card-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="card-content">
                            <h3>多端支持</h3>
                            <p>支持iPad、安卓Pad、Windows、Mac、Car等多种设备</p>
                        </div>
                    </div>
                    <div class="overview-card">
                        <div class="card-icon">
                            <i class="fas fa-code"></i>
                        </div>
                        <div class="card-content">
                            <h3>RESTful API</h3>
                            <p>标准的REST API设计，易于集成和使用</p>
                        </div>
                    </div>
                </div>

                <div class="system-info-section">
                    <div class="section-header">
                        <h2>系统信息</h2>
                        <button class="btn btn-primary" id="refresh-system-info">
                            <i class="fas fa-sync-alt"></i>
                            刷新
                        </button>
                    </div>
                    <div class="system-info-grid">
                        <div class="system-info-card">
                            <div class="info-icon">
                                <i class="fas fa-server"></i>
                            </div>
                            <div class="info-content">
                                <div class="info-label">系统架构</div>
                                <div class="info-value" id="system-arch">获取中...</div>
                            </div>
                        </div>
                        <div class="system-info-card">
                            <div class="info-icon">
                                <i class="fas fa-network-wired"></i>
                            </div>
                            <div class="info-content">
                                <div class="info-label">运行端口</div>
                                <div class="info-value" id="system-port">获取中...</div>
                            </div>
                        </div>
                        <div class="system-info-card">
                            <div class="info-icon">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <div class="info-content">
                                <div class="info-label">运行模式</div>
                                <div class="info-value" id="system-mode">获取中...</div>
                            </div>
                        </div>
                        <div class="system-info-card">
                            <div class="info-icon">
                                <i class="fas fa-memory"></i>
                            </div>
                            <div class="info-content">
                                <div class="info-label">系统内存</div>
                                <div class="info-value" id="system-memory">获取中...</div>
                            </div>
                        </div>
                        <div class="system-info-card">
                            <div class="info-icon">
                                <i class="fas fa-database"></i>
                            </div>
                            <div class="info-content">
                                <div class="info-label">Redis内存</div>
                                <div class="info-value" id="redis-memory">获取中...</div>
                            </div>
                        </div>
                        <div class="system-info-card">
                            <div class="info-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="info-content">
                                <div class="info-label">运行时间</div>
                                <div class="info-value" id="system-uptime">获取中...</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="accounts-section">
                    <div class="section-header">
                        <h2>已登录账号</h2>
                        <div class="header-buttons">
                            <button class="btn btn-success" id="add-account-btn">
                                <i class="fas fa-plus"></i>
                                添加账号
                            </button>
                            <button class="btn btn-primary" id="refresh-accounts-overview">
                                <i class="fas fa-sync-alt"></i>
                                刷新
                            </button>
                        </div>
                    </div>
                    <div class="accounts-overview" id="accounts-overview">
                        <!-- 动态生成账号列表 -->
                    </div>
                </div>

                <div class="stats-section">
                    <h2>接口统计</h2>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-number" id="total-apis">0</div>
                            <div class="stat-label">总接口数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="total-categories">0</div>
                            <div class="stat-label">分类数量</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="login-apis">0</div>
                            <div class="stat-label">登录相关</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="message-apis">0</div>
                            <div class="stat-label">消息相关</div>
                        </div>

                    </div>
                </div>
            </section>

            <!-- 好友管理页面 -->
            <section id="friends-section" class="content-section">
                <div class="friends-header">
                    <h1>好友管理</h1>
                    <p>搜索好友并发送消息</p>
                </div>

                <!-- 搜索好友区域 -->
                <div class="search-friends-section">
                    <div class="section-header">
                        <h2>搜索好友</h2>
                    </div>
                    <div class="search-form-container">
                        <div class="search-form">
                            <div class="form-group">
                                <label for="search-wxid-select">选择账号：</label>
                                <select id="search-wxid-select" class="form-control">
                                    <option value="">请选择登录账号</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="search-target-input">搜索目标：</label>
                                <input type="text" id="search-target-input" class="form-control"
                                       placeholder="请输入微信号、手机号或昵称">
                                <small class="form-help">支持微信号、手机号等方式搜索</small>
                            </div>
                            <div class="form-actions">
                                <button type="button" id="search-friend-btn" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                    搜索好友
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 搜索结果区域 -->
                <div class="search-results-section" id="search-results-section" style="display: none;">
                    <div class="section-header">
                        <h2>搜索结果</h2>
                    </div>
                    <div class="search-results" id="search-results">
                        <!-- 动态生成搜索结果 -->
                    </div>
                </div>

                <!-- 发送消息区域 -->
                <div class="send-message-section" id="send-message-section" style="display: none;">
                    <div class="section-header">
                        <h2>发送消息</h2>
                    </div>
                    <div class="message-form-container">
                        <div class="selected-friend-info" id="selected-friend-info">
                            <!-- 显示选中的好友信息 -->
                        </div>
                        <div class="message-form">
                            <div class="form-group">
                                <label for="message-content">消息内容：</label>
                                <textarea id="message-content" class="form-control" rows="4"
                                          placeholder="请输入要发送的消息内容"></textarea>
                            </div>
                            <div class="form-actions">
                                <button type="button" id="send-message-btn" class="btn btn-success">
                                    <i class="fas fa-paper-plane"></i>
                                    发送消息
                                </button>
                                <button type="button" id="clear-selection-btn" class="btn btn-secondary">
                                    <i class="fas fa-times"></i>
                                    清除选择
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- API文档页面 -->
            <section id="apis-section" class="content-section">
                <div class="apis-header">
                    <h1>接口文档</h1>
                    <div class="apis-toolbar">
                        <div class="filter-group">
                            <select id="method-filter">
                                <option value="">所有方法</option>
                                <option value="GET">GET</option>
                                <option value="POST">POST</option>
                                <option value="PUT">PUT</option>
                                <option value="DELETE">DELETE</option>
                            </select>
                            <select id="category-filter">
                                <option value="">所有分类</option>
                            </select>
                        </div>
                        <div class="view-toggle">
                            <button class="view-btn active" data-view="list">
                                <i class="fas fa-list"></i>
                            </button>
                            <button class="view-btn" data-view="grid">
                                <i class="fas fa-th"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="apis-content" id="apis-content">
                    <!-- 动态生成API列表 -->
                </div>
            </section>

            <!-- 配置管理页面 -->
            <section id="config-section" class="content-section">
                <div class="config-header">
                    <h1>修改配置项</h1>
                    <p class="config-subtitle">管理系统配置文件和配置项</p>
                </div>

                <div class="config-toolbar">
                    <div class="config-actions">
                        <button class="btn btn-primary" id="refresh-config">
                            <i class="fas fa-sync-alt"></i>
                            刷新配置
                        </button>
                        <button class="btn btn-success" id="backup-config">
                            <i class="fas fa-download"></i>
                            备份配置
                        </button>
                        <button class="btn btn-info" id="save-all-config">
                            <i class="fas fa-save"></i>
                            保存全部
                        </button>
                        <button class="btn btn-warning" id="expand-all-config">
                            <i class="fas fa-expand-arrows-alt"></i>
                            展开全部
                        </button>
                        <button class="btn btn-secondary" id="collapse-all-config">
                            <i class="fas fa-compress-arrows-alt"></i>
                            收起全部
                        </button>
                    </div>
                    <div class="config-search">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="搜索配置项..." id="config-search">
                    </div>
                </div>

                <div class="config-content" id="config-content">
                    <div class="config-loading">
                        <i class="fas fa-spinner fa-spin fa-2x"></i>
                        <p>正在加载配置...</p>
                    </div>
                </div>
            </section>

        </div>
    </main>

    <!-- 账号详情模态框 -->
    <div class="api-modal" id="account-detail-modal">
        <div class="api-modal-content">
            <div class="api-modal-header">
                <h3 id="account-detail-title">账号详情</h3>
                <button class="close-btn" id="close-account-detail-modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="api-modal-body" id="account-detail-content">
                <!-- 动态生成账号详情内容 -->
            </div>
        </div>
    </div>

    <!-- API详情模态框 -->
    <div class="api-modal" id="api-modal">
        <div class="api-modal-content">
            <div class="api-modal-header">
                <h3 id="api-modal-title"></h3>
                <button class="close-btn" id="close-api-modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="api-modal-body" id="api-modal-body">
                <!-- 动态生成API详情内容 -->
            </div>
        </div>
    </div>

    <!-- 添加账号模态框 -->
    <div class="api-modal" id="add-account-modal">
        <div class="api-modal-content add-account-modal-content">
            <div class="api-modal-header">
                <h3>添加账号</h3>
                <button class="close-btn" id="close-add-account-modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="api-modal-body" id="add-account-modal-body">
                <!-- 登录方式选择 -->
                <div class="login-method-selector">
                    <div class="method-tabs">
                        <button class="method-tab active" data-method="qrcode">
                            <i class="fas fa-qrcode"></i>
                            扫码登录
                        </button>
                        <button class="method-tab" data-method="password">
                            <i class="fas fa-key"></i>
                            账号密码登录
                        </button>
                    </div>
                </div>

                <!-- 设备版本选择 -->
                <div class="device-version-selector">
                    <label for="device-version-select">设备版本：</label>
                    <select id="device-version-select" class="form-control">
                        <option value="ipad">iPad</option>
                        <option value="ipadx">iPad (绕过验证码)</option>
                        <option value="android">Android</option>
                        <option value="androidpad">Android Pad</option>
                        <option value="androidpadx">Android Pad (绕过验证码)</option>
                        <option value="windows">Windows</option>
                        <option value="windowsuwp">Windows UWP (绕过验证码)</option>
                        <option value="windowsunified">Windows 统一版</option>
                        <option value="mac">Mac</option>
                        <option value="car">Car</option>
                    </select>
                </div>

                <!-- 设备信息配置 - 公共区域 -->
                <div class="device-info-section">
                    <div class="section-title">
                        <i class="fas fa-mobile-alt"></i>
                        设备信息配置
                    </div>
                    <div class="form-group">
                        <label for="device-id-input">设备ID：</label>
                        <div class="input-group">
                            <input type="text" id="device-id-input" class="form-control" placeholder="设备唯一标识符"
                                maxlength="32">
                            <button type="button" class="btn btn-outline-secondary btn-sm" id="regenerate-device-id-btn"
                                title="重新生成设备ID">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                        <small class="form-text text-muted">设备的唯一标识符，用于区分不同设备</small>
                    </div>
                    <div class="form-group">
                        <label for="device-name-input">设备名称：</label>
                        <div class="input-group">
                            <input type="text" id="device-name-input" class="form-control" placeholder="设备显示名称"
                                maxlength="50">
                            <button type="button" class="btn btn-outline-secondary btn-sm"
                                id="regenerate-device-name-btn" title="重新生成设备名称">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                        <small class="form-text text-muted">设备的显示名称，便于识别</small>
                    </div>
                    <div class="form-group">
                        <div class="device-info-actions">
                            <button type="button" class="btn btn-outline-primary btn-sm"
                                id="regenerate-all-device-info-btn">
                                <i class="fas fa-refresh"></i>
                                重新生成所有设备信息
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 扫码登录内容 -->
                <div class="login-content" id="qrcode-login-content">
                    <!-- 登录进度指示器 -->
                    <div class="login-progress-container" id="login-progress-container" style="display: none;">
                        <div class="progress-steps">
                            <div class="progress-step active" data-step="1">
                                <div class="step-icon">
                                    <i class="fas fa-qrcode"></i>
                                </div>
                                <div class="step-text">获取二维码</div>
                            </div>
                            <div class="progress-step" data-step="2">
                                <div class="step-icon">
                                    <i class="fas fa-mobile-alt"></i>
                                </div>
                                <div class="step-text">扫描二维码</div>
                            </div>
                            <div class="progress-step" data-step="3">
                                <div class="step-icon">
                                    <i class="fas fa-check"></i>
                                </div>
                                <div class="step-text">确认登录</div>
                            </div>
                            <div class="progress-step" data-step="4">
                                <div class="step-icon">
                                    <i class="fas fa-user-check"></i>
                                </div>
                                <div class="step-text">初始化账号</div>
                            </div>
                        </div>
                        <div class="progress-line">
                            <div class="progress-line-fill" id="progress-line-fill"></div>
                        </div>
                    </div>

                    <div class="qrcode-container">
                        <div class="qrcode-display" id="qrcode-display">
                            <!-- 二维码骨架屏将通过JavaScript显示 -->
                        </div>
                        <div class="qrcode-status" id="qrcode-status">
                            等待获取二维码
                        </div>
                        <div class="qrcode-actions" style="display: none;">
                            <span class="qrcode-action-link" id="refresh-qrcode-btn">
                                <i class="fas fa-sync-alt"></i>
                                刷新二维码
                            </span>
                        </div>
                    </div>
                </div>

                <!-- 账号密码登录内容 -->
                <div class="login-content" id="password-login-content" style="display: none;">
                    <form class="login-form" id="password-login-form">
                        <div class="form-group">
                            <label for="username-input">用户名/手机号：</label>
                            <input type="text" id="username-input" class="form-control" placeholder="请输入用户名或手机号"
                                required>
                        </div>
                        <div class="form-group">
                            <label for="password-input">密码：</label>
                            <input type="password" id="password-input" class="form-control" placeholder="请输入密码"
                                required>
                        </div>

                        <div class="form-group">
                            <label for="proxy-input">代理设置：</label>
                            <input type="text" id="proxy-input" class="form-control"
                                placeholder="代理地址（可选，格式：host:port）">
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-sign-in-alt"></i>
                                登录
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载动画 -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>加载中...</p>
        </div>
    </div>

    <!-- 退出登录确认模态框 -->
    <div class="modal-overlay" id="logout-modal" style="display: none;">
        <div class="modal-container">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-sign-out-alt text-warning"></i>
                    确认退出登录
                </h3>
                <button class="modal-close-btn" id="logout-modal-close-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="logout-account-info">
                    <div class="account-preview">
                        <div class="account-avatar" id="logout-account-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="account-details">
                            <div class="account-name" id="logout-account-name">账号名称</div>
                            <div class="account-wxid" id="logout-account-wxid">微信号</div>
                        </div>
                    </div>
                    <div class="warning-message">
                        <div class="warning-title">此操作将会：</div>
                        <ul class="warning-list">
                            <li><i class="fas fa-sign-out-alt"></i> 退出该账号的登录状态</li>
                            <li><i class="fas fa-refresh"></i> 账号状态变为离线</li>
                            <li><i class="fas fa-info-circle"></i> 可以重新登录恢复在线状态</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancel-logout-btn">
                    <i class="fas fa-times"></i>
                    取消
                </button>
                <button class="btn btn-warning" id="confirm-logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    确认退出
                </button>
            </div>
        </div>
    </div>

    <!-- Toast 通知 -->
    <div class="toast-container" id="toast-container"></div>

    <!-- 模块化脚本引用 -->
    <script src="js/friends.js"></script>
    <script type="module" src="js/core.js"></script>
</body>

</html>