package controllers

import (
	"net/http"
	"os"
	"path/filepath"
	"strings"

	"github.com/astaxie/beego"
	log "github.com/sirupsen/logrus"
)

// StaticController 静态文件服务控制器
type StaticController struct {
	beego.Controller
}

// ServeStatic 提供静态文件服务
func (c *StaticController) ServeStatic() {
	// 获取请求的路径
	requestPath := c.Ctx.Input.Param(":splat")
	if requestPath == "" {
		requestPath = "index.html"
	}

	// 静态文件根目录
	staticRoot := "element-plus-vite-starter/dist"

	// 构建完整的文件路径
	filePath := filepath.Join(staticRoot, requestPath)

	// 安全检查：防止路径遍历攻击
	absStaticRoot, err := filepath.Abs(staticRoot)
	if err != nil {
		log.Errorf("获取静态文件根目录绝对路径失败: %v", err)
		c.Ctx.Output.SetStatus(500)
		c.Ctx.Output.Body([]byte("Internal Server Error"))
		return
	}

	absFilePath, err := filepath.Abs(filePath)
	if err != nil {
		log.Errorf("获取文件绝对路径失败: %v", err)
		c.Ctx.Output.SetStatus(500)
		c.Ctx.Output.Body([]byte("Internal Server Error"))
		return
	}

	// 确保请求的文件在静态文件目录内
	if !strings.HasPrefix(absFilePath, absStaticRoot) {
		log.Warnf("检测到路径遍历攻击尝试: %s", requestPath)
		c.Ctx.Output.SetStatus(403)
		c.Ctx.Output.Body([]byte("Forbidden"))
		return
	}

	// 检查文件是否存在
	if _, err := os.Stat(absFilePath); os.IsNotExist(err) {
		// 如果是SPA路由，返回index.html
		if c.isSPARoute(requestPath) {
			indexPath := filepath.Join(staticRoot, "index.html")
			c.serveFile(indexPath, "text/html")
			return
		}

		log.Warnf("请求的文件不存在: %s", absFilePath)
		c.Ctx.Output.SetStatus(404)
		c.Ctx.Output.Body([]byte("File Not Found"))
		return
	}

	// 提供文件服务
	contentType := c.getContentType(absFilePath)
	c.serveFile(absFilePath, contentType)
}

// isSPARoute 判断是否是SPA路由
func (c *StaticController) isSPARoute(path string) bool {
	// 如果路径不包含文件扩展名，且不是API路径，则认为是SPA路由
	if strings.Contains(path, ".") {
		return false
	}

	// 排除API路径
	if strings.HasPrefix(path, "api/") {
		return false
	}

	return true
}

// serveFile 提供文件服务
func (c *StaticController) serveFile(filePath, contentType string) {
	file, err := os.Open(filePath)
	if err != nil {
		log.Errorf("打开文件失败: %v", err)
		c.Ctx.Output.SetStatus(500)
		c.Ctx.Output.Body([]byte("Internal Server Error"))
		return
	}
	defer file.Close()

	// 获取文件信息
	fileInfo, err := file.Stat()
	if err != nil {
		log.Errorf("获取文件信息失败: %v", err)
		c.Ctx.Output.SetStatus(500)
		c.Ctx.Output.Body([]byte("Internal Server Error"))
		return
	}

	// 设置响应头
	c.Ctx.Output.Header("Content-Type", contentType)
	c.Ctx.Output.Header("Cache-Control", c.getCacheControl(filePath))

	// 添加CORS头
	c.Ctx.Output.Header("Access-Control-Allow-Origin", "*")
	c.Ctx.Output.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	c.Ctx.Output.Header("Access-Control-Allow-Headers", "Origin, Authorization, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type")

	// 使用http.ServeContent提供文件服务，支持Range请求
	http.ServeContent(c.Ctx.ResponseWriter, c.Ctx.Request, fileInfo.Name(), fileInfo.ModTime(), file)
}

// getContentType 根据文件扩展名获取Content-Type
func (c *StaticController) getContentType(filePath string) string {
	ext := strings.ToLower(filepath.Ext(filePath))

	contentTypes := map[string]string{
		".html":  "text/html; charset=utf-8",
		".css":   "text/css; charset=utf-8",
		".js":    "application/javascript; charset=utf-8",
		".json":  "application/json; charset=utf-8",
		".png":   "image/png",
		".jpg":   "image/jpeg",
		".jpeg":  "image/jpeg",
		".gif":   "image/gif",
		".svg":   "image/svg+xml",
		".ico":   "image/x-icon",
		".woff":  "font/woff",
		".woff2": "font/woff2",
		".ttf":   "font/ttf",
		".eot":   "application/vnd.ms-fontobject",
		".map":   "application/json",
	}

	if contentType, exists := contentTypes[ext]; exists {
		return contentType
	}

	return "application/octet-stream"
}

// getCacheControl 根据文件类型获取缓存控制头
func (c *StaticController) getCacheControl(filePath string) string {
	ext := strings.ToLower(filepath.Ext(filePath))

	// 静态资源缓存策略
	switch ext {
	case ".html":
		return "no-cache" // HTML文件不缓存，确保SPA路由正常工作
	case ".css", ".js":
		if strings.Contains(filePath, "assets/") {
			return "public, max-age=31536000" // 带hash的资源文件缓存1年
		}
		return "public, max-age=86400" // 普通CSS/JS文件缓存1天
	case ".png", ".jpg", ".jpeg", ".gif", ".svg", ".ico":
		return "public, max-age=2592000" // 图片缓存30天
	case ".woff", ".woff2", ".ttf", ".eot":
		return "public, max-age=31536000" // 字体文件缓存1年
	default:
		return "public, max-age=86400" // 其他文件缓存1天
	}
}

// HealthCheck 健康检查接口
func (c *StaticController) HealthCheck() {
	c.Data["json"] = map[string]interface{}{
		"status":    "ok",
		"message":   "静态文件服务运行正常",
		"timestamp": c.GetString("timestamp"),
	}
	c.ServeJSON()
}
