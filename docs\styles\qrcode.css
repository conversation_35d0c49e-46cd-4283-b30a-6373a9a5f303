/* 二维码相关样式 */

/* 二维码显示区域基础样式 */
.qrcode-display {
  width: 280px;
  height: 280px;
  max-width: 90vw; /* 响应式宽度 */
  max-height: 90vw; /* 响应式高度 */
  background: var(--bg-primary);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-medium);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
  aspect-ratio: 1; /* 保持正方形比例 */
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.qrcode-display:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* 二维码加载状态 */
.qrcode-display.loading {
  border-color: var(--primary-color);
  animation: qrcodePulse 2s ease-in-out infinite;
}

/* 二维码扫描状态 */
.qrcode-display.scanned {
  border-color: var(--success-color);
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
}

/* 二维码过期状态 */
.qrcode-display.expired {
  border-color: var(--error-color);
  box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
}

/* 确保二维码显示区域支持覆盖层 */
.qrcode-display {
  position: relative; /* 确保覆盖层能正确定位 */
}

/* 通用覆盖层基础样式 */
.qrcode-display .user-scanned-overlay,
.qrcode-display .qrcode-cancelled-overlay,
.qrcode-display .qrcode-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: inherit; /* 继承父容器的圆角 */
  z-index: 10;
}

/* 二维码显示容器 */
.qrcode-display-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 20px;
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-medium);
  box-sizing: border-box;
}

/* 二维码图片包装器 */
.qrcode-image-wrapper {
  width: 200px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  border-radius: var(--radius-small);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-light);
  overflow: hidden;
  flex-shrink: 0;
}

.qrcode-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: block;
}

/* 二维码提示信息 */
.qrcode-tips {
  text-align: center;
  color: var(--text-secondary);
}

.qrcode-tips i {
  font-size: 20px;
  margin-bottom: 8px;
  color: var(--primary-color);
}

.qrcode-tips p {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

/* 二维码容器样式 */
.qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  width: 100%;
  max-width: 380px;
  margin: 0 auto;
  padding: 24px;
  box-sizing: border-box;
  background: var(--bg-primary);
  border-radius: var(--radius-large);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 二维码状态样式 */
.qrcode-status {
  text-align: center;
  margin: 16px 0;
  font-size: 15px;
  color: var(--text-secondary);
  font-weight: 500;
  padding: 12px 20px;
  border-radius: var(--radius-medium);
  background: rgba(0, 0, 0, 0.02);
  border: 1px solid transparent;
  transition: all 0.3s ease;
  min-height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.qrcode-status.success {
  color: var(--success-color);
  background: rgba(82, 196, 26, 0.1);
  border-color: rgba(82, 196, 26, 0.2);
}

.qrcode-status.warning {
  color: var(--warning-color);
  background: rgba(250, 173, 20, 0.1);
  border-color: rgba(250, 173, 20, 0.2);
}

.qrcode-status.error {
  color: var(--error-color);
  background: rgba(255, 77, 79, 0.1);
  border-color: rgba(255, 77, 79, 0.2);
}

/* 状态图标 */
.qrcode-status i {
  font-size: 16px;
}

.qrcode-status.success i {
  animation: successBounce 0.6s ease-out;
}

.qrcode-status.warning i {
  animation: warningPulse 1.5s ease-in-out infinite;
}

.qrcode-status.error i {
  animation: errorShake 0.5s ease-in-out;
}

/* 二维码操作链接样式 */
.qrcode-action-link {
  color: var(--primary-color);
  cursor: pointer;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  border-bottom: 1px solid transparent;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 4px;
  border-radius: var(--radius-small);
}

.qrcode-action-link:hover {
  background: rgba(24, 144, 255, 0.1);
  border-bottom-color: var(--primary-color);
  transform: translateY(-1px);
}

.qrcode-action-link:active {
  transform: translateY(0);
}

.qrcode-action-link[disabled],
.qrcode-action-link.disabled {
  color: var(--text-tertiary);
  cursor: not-allowed;
  opacity: 0.6;
  border-bottom-color: transparent;
  transform: none;
}

.qrcode-action-link[disabled]:hover {
  color: var(--text-tertiary);
  border-bottom-color: transparent;
  transform: none;
}

/* 二维码操作按钮区域 */
.qrcode-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 16px;
}

/* 二维码错误状态 */
.qrcode-error {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  color: var(--error-color);
  text-align: center;
  padding: 20px;
}

.qrcode-error i {
  font-size: 48px;
  opacity: 0.6;
}

.qrcode-error h4 {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.qrcode-error p {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

/* 用户扫码后的覆盖层样式 */
.user-scanned-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 20px;
  border-radius: var(--radius-medium);
  z-index: 10;
  animation: fadeInScale 0.3s ease-out;
}

.user-scanned-overlay .user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.user-scanned-overlay .user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  aspect-ratio: 1;
  border: 3px solid var(--primary-color);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
}

.user-scanned-overlay .avatar-fallback {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 600;
  border: 3px solid var(--primary-color);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
}

.user-scanned-overlay .user-details {
  text-align: center;
}

.user-scanned-overlay .user-nickname {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.user-scanned-overlay .scan-status {
  font-size: 14px;
  color: var(--success-color);
  font-weight: 500;
}

.user-scanned-overlay .confirm-hint {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: var(--text-secondary);
  font-size: 14px;
  text-align: center;
}

.user-scanned-overlay .confirm-hint i {
  font-size: 24px;
  color: var(--primary-color);
}

/* 取消登录覆盖层样式 */
.qrcode-cancelled-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 20px;
  padding: 20px;
  border-radius: var(--radius-medium);
  z-index: 10;
  animation: fadeInScale 0.3s ease-out;
}

.qrcode-cancelled-overlay .cancel-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  text-align: center;
}

.qrcode-cancelled-overlay .cancel-info i {
  font-size: 48px;
  color: var(--error-color);
  margin-bottom: 8px;
}

.qrcode-cancelled-overlay .cancel-message {
  font-size: 18px;
  font-weight: 600;
  color: var(--error-color);
  margin-bottom: 4px;
}

.qrcode-cancelled-overlay .cancel-user {
  font-size: 14px;
  color: var(--text-secondary);
}

.qrcode-cancelled-overlay .btn {
  margin-top: 8px;
  padding: 10px 20px;
  font-size: 14px;
}

/* 登录成功覆盖层样式 */
.qrcode-success-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 20px;
  padding: 20px;
  border-radius: var(--radius-medium);
  z-index: 10;
  animation: fadeInScale 0.3s ease-out;
}

.qrcode-success-overlay .success-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  text-align: center;
}

.qrcode-success-overlay .success-info i {
  font-size: 48px;
  color: var(--success-color);
  margin-bottom: 8px;
  animation: successPulse 1.5s ease-in-out infinite;
}

.qrcode-success-overlay .success-message {
  font-size: 18px;
  font-weight: 600;
  color: var(--success-color);
  margin-bottom: 4px;
}

.qrcode-success-overlay .success-user {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
}

.qrcode-success-overlay .success-hint {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  width: 100%;
}

/* 进度指示器样式 */
.progress-indicator {
  width: 120px;
  height: 4px;
  background: rgba(24, 144, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
  position: relative;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), #40a9ff);
  border-radius: 2px;
  animation: progressMove 2s ease-in-out infinite;
  width: 30%;
}

.progress-text {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
  text-align: center;
}

/* 动画效果 */
@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes successPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes progressMove {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(200%);
  }
  100% {
    transform: translateX(-100%);
  }
}

@keyframes qrcodePulse {
  0%, 100% {
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
  }
  50% {
    border-color: #40a9ff;
    box-shadow: 0 4px 20px rgba(24, 144, 255, 0.4);
  }
}

@keyframes successBounce {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.3);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes warningPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

@keyframes errorShake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-4px);
  }
  75% {
    transform: translateX(4px);
  }
}

/* 登录进度指示器样式 */
.login-progress-container {
  width: 100%;
  max-width: 500px;
  margin: 0 auto 30px;
  padding: 20px;
  background: var(--bg-primary);
  border-radius: var(--radius-large);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  position: relative;
}

.progress-steps {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 2;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  flex: 1;
  position: relative;
}

.step-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--bg-secondary);
  border: 2px solid var(--border-light);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-tertiary);
  font-size: 16px;
  transition: all 0.3s ease;
  position: relative;
  z-index: 3;
}

.step-text {
  font-size: 12px;
  color: var(--text-tertiary);
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
}

/* 激活状态 */
.progress-step.active .step-icon {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
  animation: stepPulse 2s ease-in-out infinite;
}

.progress-step.active .step-text {
  color: var(--primary-color);
  font-weight: 600;
}

/* 完成状态 */
.progress-step.completed .step-icon {
  background: var(--success-color);
  border-color: var(--success-color);
  color: white;
}

.progress-step.completed .step-text {
  color: var(--success-color);
  font-weight: 600;
}

/* 进度线 */
.progress-line {
  position: absolute;
  top: 50%;
  left: 20px;
  right: 20px;
  height: 2px;
  background: var(--border-light);
  transform: translateY(-50%);
  z-index: 1;
}

.progress-line-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--success-color));
  width: 0%;
  transition: width 0.8s ease;
  border-radius: 1px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .progress-steps {
    gap: 8px;
  }

  .step-icon {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }

  .step-text {
    font-size: 11px;
  }

  .login-progress-container {
    padding: 16px;
  }
}

/* 步骤动画 */
@keyframes stepPulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(24, 144, 255, 0);
  }
}

/* 登录成功通知样式 */
.login-success-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: white;
  border-radius: var(--radius-large);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid var(--success-color);
  min-width: 320px;
  max-width: 400px;
  z-index: 9999;
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.3s ease;
}

.login-success-notification.show {
  transform: translateX(0);
  opacity: 1;
}

.notification-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
}

.notification-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  background: var(--success-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.notification-text {
  flex: 1;
}

.notification-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.notification-message {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 8px;
}

.notification-action {
  font-size: 12px;
  color: var(--success-color);
  font-weight: 500;
}

.notification-close {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-tertiary);
  font-size: 12px;
  transition: all 0.2s ease;
}

.notification-close:hover {
  background: rgba(0, 0, 0, 0.2);
  color: var(--text-secondary);
}

/* 移动端适配 */
@media (max-width: 480px) {
  .login-success-notification {
    top: 10px;
    right: 10px;
    left: 10px;
    min-width: auto;
    max-width: none;
  }

  .notification-content {
    padding: 16px;
    gap: 12px;
  }

  .notification-icon {
    width: 32px;
    height: 32px;
    font-size: 16px;
  }
}
