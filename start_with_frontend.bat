@echo off
chcp 65001 >nul
echo ========================================
echo 微信API服务 + 前端静态文件服务启动脚本
echo ========================================
echo.

:: 检查前端构建文件是否存在
if not exist "element-plus-vite-starter\dist\index.html" (
    echo [错误] 前端构建文件不存在！
    echo 请先运行以下命令构建前端：
    echo   cd element-plus-vite-starter
    echo   npm run build
    echo.
    pause
    exit /b 1
)

echo [信息] 前端构建文件检查通过
echo.

:: 显示服务信息
echo [服务信息]
echo API服务端口: 8059 (可在 conf/app.conf 中修改)
echo 前端服务端口: 3000 (固定)
echo WebSocket端口: 8088 (可在 conf/app.conf 中修改)
echo.

:: 显示访问地址
echo [访问地址]
echo 前端界面: http://localhost:3000
echo API文档: http://localhost:8059/
echo 健康检查: http://localhost:3000/health
echo.

:: 检查端口占用
echo [端口检查]
netstat -an | findstr ":3000 " >nul
if %errorlevel% == 0 (
    echo [警告] 端口 3000 已被占用，可能影响前端服务启动
)

netstat -an | findstr ":8059 " >nul
if %errorlevel% == 0 (
    echo [警告] 端口 8059 已被占用，可能影响API服务启动
)

netstat -an | findstr ":8088 " >nul
if %errorlevel% == 0 (
    echo [警告] 端口 8088 已被占用，可能影响WebSocket服务启动
)

echo.
echo [启动中] 正在启动微信API服务...
echo 按 Ctrl+C 可以停止服务
echo.

:: 启动Go程序
if exist "wechat_win.exe" (
    wechat_win.exe
) else if exist "wechat_dev.exe" (
    wechat_dev.exe
) else (
    echo [错误] 找不到可执行文件！
    echo 请确保存在以下文件之一：
    echo   - wechat_win.exe
    echo   - wechat_dev.exe
    echo.
    pause
    exit /b 1
)

echo.
echo [信息] 服务已停止
pause
