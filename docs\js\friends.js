// 好友管理功能
class FriendsManager {
  constructor(app) {
    this.app = app;
    this.selectedFriend = null;
    this.searchResults = [];
    this.init();
  }

  init() {
    console.log('[FriendsManager] 初始化好友管理器');
    this.setupEventListeners();
    this.loadAccountOptions();
  }

  setupEventListeners() {
    // 搜索好友按钮
    const searchBtn = document.getElementById('search-friend-btn');
    if (searchBtn) {
      searchBtn.addEventListener('click', () => this.searchFriend());
    }

    // 发送消息按钮
    const sendBtn = document.getElementById('send-message-btn');
    if (sendBtn) {
      sendBtn.addEventListener('click', () => this.sendMessage());
    }

    // 清除选择按钮
    const clearBtn = document.getElementById('clear-selection-btn');
    if (clearBtn) {
      clearBtn.addEventListener('click', () => this.clearSelection());
    }

    // 搜索输入框回车事件
    const searchInput = document.getElementById('search-target-input');
    if (searchInput) {
      searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          this.searchFriend();
        }
      });
    }
  }

  // 加载账号选项
  async loadAccountOptions() {
    try {
      const response = await fetch('/api/Login/GetLoginList', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
      });

      const result = await response.json();
      const select = document.getElementById('search-wxid-select');
      
      if (!select) return;

      // 清空现有选项
      select.innerHTML = '<option value="">请选择登录账号</option>';

      if (result.Success && result.Data && Array.isArray(result.Data)) {
        result.Data.forEach(account => {
          const option = document.createElement('option');
          option.value = account.Wxid;
          option.textContent = `${account.NickName || account.Wxid} (${account.Wxid})`;
          select.appendChild(option);
        });

        // 如果只有一个账号，自动选择
        if (result.Data.length === 1) {
          select.value = result.Data[0].Wxid;
        }
      }
    } catch (error) {
      console.error('加载账号列表失败:', error);
      this.app.showToast('加载账号列表失败', 'error');
    }
  }

  // 搜索好友
  async searchFriend() {
    const wxid = document.getElementById('search-wxid-select').value;
    const target = document.getElementById('search-target-input').value.trim();

    if (!wxid) {
      this.app.showToast('请选择登录账号', 'warning');
      return;
    }

    if (!target) {
      this.app.showToast('请输入搜索目标', 'warning');
      return;
    }

    try {
      this.showSearchLoading();

      const response = await fetch('/api/Friend/Search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          Wxid: wxid,
          ToUserName: target,
          FromScene: 0,
          SearchScene: 1
        })
      });

      const result = await response.json();

      if (result.Success && result.Data) {
        this.displaySearchResults([result.Data]);
        this.app.showToast('搜索完成', 'success');
      } else {
        this.displaySearchResults([]);
        this.app.showToast(result.Message || '搜索失败', 'error');
      }
    } catch (error) {
      console.error('搜索好友失败:', error);
      this.app.showToast('搜索失败: ' + error.message, 'error');
      this.displaySearchResults([]);
    }
  }

  // 显示搜索加载状态
  showSearchLoading() {
    const resultsSection = document.getElementById('search-results-section');
    const resultsContainer = document.getElementById('search-results');
    
    if (resultsSection && resultsContainer) {
      resultsSection.style.display = 'block';
      resultsContainer.innerHTML = `
        <div class="loading-state">
          <i class="fas fa-spinner fa-spin"></i>
          <p>正在搜索好友...</p>
        </div>
      `;
    }
  }

  // 显示搜索结果
  displaySearchResults(results) {
    const resultsSection = document.getElementById('search-results-section');
    const resultsContainer = document.getElementById('search-results');
    
    if (!resultsSection || !resultsContainer) return;

    resultsSection.style.display = 'block';
    this.searchResults = results;

    if (results.length === 0) {
      resultsContainer.innerHTML = `
        <div class="empty-state">
          <i class="fas fa-user-slash"></i>
          <h3>未找到好友</h3>
          <p>请检查搜索条件或尝试其他关键词</p>
        </div>
      `;
      return;
    }

    resultsContainer.innerHTML = results.map((friend, index) => 
      this.renderFriendCard(friend, index)
    ).join('');

    // 添加点击事件
    resultsContainer.querySelectorAll('.friend-result-card').forEach((card, index) => {
      card.addEventListener('click', () => this.selectFriend(results[index], card));
    });
  }

  // 渲染好友卡片
  renderFriendCard(friend, index) {
    const statusInfo = this.getFriendStatusInfo(friend);
    const avatarUrl = friend.SmallHeadImgUrl || friend.BigHeadImgUrl;
    
    return `
      <div class="friend-result-card" data-index="${index}">
        <div class="friend-card-header">
          <div class="friend-avatar">
            ${avatarUrl ? 
              `<img src="${avatarUrl}" alt="${friend.NickName || friend.UserName}" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
               <i class="fas fa-user" style="display: none;"></i>` :
              `<i class="fas fa-user"></i>`
            }
          </div>
          <div class="friend-info">
            <div class="friend-nickname">${friend.NickName || friend.UserName || '未知用户'}</div>
            <div class="friend-wxid">${friend.UserName || friend.Alias || ''}</div>
          </div>
        </div>
        <div class="friend-status">
          <span class="friend-status-badge ${statusInfo.class}">${statusInfo.text}</span>
          ${friend.Alias ? `<span class="friend-alias">别名: ${friend.Alias}</span>` : ''}
        </div>
        <div class="friend-actions">
          ${statusInfo.canMessage ? 
            `<button class="friend-action-btn primary" onclick="event.stopPropagation(); window.apiApp.friendsManager.selectFriendForMessage(${index})">
              <i class="fas fa-comment"></i> 发送消息
            </button>` : ''
          }
          ${statusInfo.canAdd ? 
            `<button class="friend-action-btn secondary" onclick="event.stopPropagation(); window.apiApp.friendsManager.addFriend(${index})">
              <i class="fas fa-user-plus"></i> 添加好友
            </button>` : ''
          }
        </div>
      </div>
    `;
  }

  // 获取好友状态信息
  getFriendStatusInfo(friend) {
    const sex = friend.Sex;
    
    if (sex === 1) {
      return {
        class: 'friend',
        text: '已是好友',
        canMessage: true,
        canAdd: false
      };
    } else if (sex === 2) {
      return {
        class: 'stranger',
        text: '陌生人',
        canMessage: false,
        canAdd: true
      };
    } else {
      return {
        class: 'not-found',
        text: '用户不存在',
        canMessage: false,
        canAdd: false
      };
    }
  }

  // 选择好友发送消息
  selectFriendForMessage(index) {
    const friend = this.searchResults[index];
    if (friend && friend.Sex === 1) {
      this.selectFriend(friend);
    } else {
      this.app.showToast('只能向好友发送消息', 'warning');
    }
  }

  // 选择好友
  selectFriend(friend, cardElement = null) {
    if (friend.Sex !== 1) {
      this.app.showToast('只能向好友发送消息', 'warning');
      return;
    }

    this.selectedFriend = friend;

    // 更新卡片选中状态
    document.querySelectorAll('.friend-result-card').forEach(card => {
      card.classList.remove('selected');
    });
    
    if (cardElement) {
      cardElement.classList.add('selected');
    } else {
      // 如果没有传入卡片元素，找到对应的卡片
      const cards = document.querySelectorAll('.friend-result-card');
      const index = this.searchResults.findIndex(f => f.UserName === friend.UserName);
      if (index >= 0 && cards[index]) {
        cards[index].classList.add('selected');
      }
    }

    // 显示发送消息区域
    this.showSendMessageSection();
  }

  // 显示发送消息区域
  showSendMessageSection() {
    const section = document.getElementById('send-message-section');
    const friendInfo = document.getElementById('selected-friend-info');
    
    if (!section || !friendInfo || !this.selectedFriend) return;

    const friend = this.selectedFriend;
    const avatarUrl = friend.SmallHeadImgUrl || friend.BigHeadImgUrl;

    friendInfo.innerHTML = `
      <div class="friend-avatar">
        ${avatarUrl ? 
          `<img src="${avatarUrl}" alt="${friend.NickName}" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
           <i class="fas fa-user" style="display: none;"></i>` :
          `<i class="fas fa-user"></i>`
        }
      </div>
      <div class="friend-info">
        <div class="friend-nickname">${friend.NickName || friend.UserName}</div>
        <div class="friend-wxid">${friend.UserName}</div>
      </div>
    `;

    section.style.display = 'block';
    
    // 滚动到发送消息区域
    section.scrollIntoView({ behavior: 'smooth', block: 'start' });
  }

  // 发送消息
  async sendMessage() {
    if (!this.selectedFriend) {
      this.app.showToast('请先选择好友', 'warning');
      return;
    }

    const wxid = document.getElementById('search-wxid-select').value;
    const content = document.getElementById('message-content').value.trim();

    if (!content) {
      this.app.showToast('请输入消息内容', 'warning');
      return;
    }

    try {
      const response = await fetch('/api/Message/SendText', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          Wxid: wxid,
          ToUserName: this.selectedFriend.UserName,
          Content: content
        })
      });

      const result = await response.json();

      if (result.Success) {
        this.app.showToast('消息发送成功', 'success');
        document.getElementById('message-content').value = '';
      } else {
        this.app.showToast('消息发送失败: ' + (result.Message || '未知错误'), 'error');
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      this.app.showToast('发送消息失败: ' + error.message, 'error');
    }
  }

  // 清除选择
  clearSelection() {
    this.selectedFriend = null;
    
    // 清除卡片选中状态
    document.querySelectorAll('.friend-result-card').forEach(card => {
      card.classList.remove('selected');
    });

    // 隐藏发送消息区域
    const section = document.getElementById('send-message-section');
    if (section) {
      section.style.display = 'none';
    }

    // 清空消息内容
    const messageContent = document.getElementById('message-content');
    if (messageContent) {
      messageContent.value = '';
    }
  }

  // 添加好友
  async addFriend(index) {
    const friend = this.searchResults[index];
    if (!friend || friend.Sex === 1) {
      this.app.showToast('该用户已是好友', 'info');
      return;
    }

    // 这里可以实现添加好友的逻辑
    this.app.showToast('添加好友功能待实现', 'info');
  }
}
