#!/bin/bash

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================"
echo -e "微信API服务 + 前端静态文件服务启动脚本"
echo -e "========================================${NC}"
echo

# 检查前端构建文件是否存在
if [ ! -f "element-plus-vite-starter/dist/index.html" ]; then
    echo -e "${RED}[错误] 前端构建文件不存在！${NC}"
    echo "请先运行以下命令构建前端："
    echo "  cd element-plus-vite-starter"
    echo "  npm run build"
    echo
    exit 1
fi

echo -e "${GRE<PERSON>}[信息] 前端构建文件检查通过${NC}"
echo

# 显示服务信息
echo -e "${BLUE}[服务信息]${NC}"
echo "API服务端口: 8059 (可在 conf/app.conf 中修改)"
echo "前端服务端口: 3000 (固定)"
echo "WebSocket端口: 8088 (可在 conf/app.conf 中修改)"
echo

# 显示访问地址
echo -e "${BLUE}[访问地址]${NC}"
echo "前端界面: http://localhost:3000"
echo "API文档: http://localhost:8059/"
echo "健康检查: http://localhost:3000/health"
echo

# 检查端口占用
echo -e "${BLUE}[端口检查]${NC}"
if lsof -i :3000 >/dev/null 2>&1; then
    echo -e "${YELLOW}[警告] 端口 3000 已被占用，可能影响前端服务启动${NC}"
fi

if lsof -i :8059 >/dev/null 2>&1; then
    echo -e "${YELLOW}[警告] 端口 8059 已被占用，可能影响API服务启动${NC}"
fi

if lsof -i :8088 >/dev/null 2>&1; then
    echo -e "${YELLOW}[警告] 端口 8088 已被占用，可能影响WebSocket服务启动${NC}"
fi

echo
echo -e "${GREEN}[启动中] 正在启动微信API服务...${NC}"
echo "按 Ctrl+C 可以停止服务"
echo

# 设置信号处理
trap 'echo -e "\n${YELLOW}[信息] 正在停止服务...${NC}"; exit 0' INT TERM

# 启动Go程序
if [ -f "wechat_linux" ]; then
    chmod +x wechat_linux
    ./wechat_linux
elif [ -f "wechat_dev" ]; then
    chmod +x wechat_dev
    ./wechat_dev
else
    echo -e "${RED}[错误] 找不到可执行文件！${NC}"
    echo "请确保存在以下文件之一："
    echo "  - wechat_linux"
    echo "  - wechat_dev"
    echo
    exit 1
fi

echo
echo -e "${YELLOW}[信息] 服务已停止${NC}"
