/* 好友管理页面样式 */

/* 页面头部 */
.friends-header {
  margin-bottom: 32px;
  text-align: center;
}

.friends-header h1 {
  color: var(--text-primary);
  margin-bottom: 8px;
  font-size: 2.5rem;
  font-weight: 600;
}

.friends-header p {
  color: var(--text-secondary);
  font-size: 1.1rem;
}

/* 搜索表单区域 */
.search-friends-section {
  background: var(--bg-primary);
  border-radius: var(--radius-large);
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--border-light);
}

.search-form-container {
  max-width: 600px;
  margin: 0 auto;
}

.search-form .form-group {
  margin-bottom: 20px;
}

.search-form .form-group:last-child {
  margin-bottom: 0;
}

.search-form label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-primary);
}

.search-form .form-control {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-medium);
  font-size: 14px;
  background: var(--bg-secondary);
  color: var(--text-primary);
  transition: all 0.3s ease;
}

.search-form .form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1);
}

.search-form .form-help {
  display: block;
  margin-top: 4px;
  font-size: 12px;
  color: var(--text-tertiary);
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 24px;
}

/* 搜索结果区域 */
.search-results-section {
  background: var(--bg-primary);
  border-radius: var(--radius-large);
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--border-light);
}

.search-results {
  display: grid;
  gap: 16px;
}

.friend-result-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-medium);
  padding: 20px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.friend-result-card:hover {
  border-color: var(--primary-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.friend-result-card.selected {
  border-color: var(--primary-color);
  background: rgba(24, 144, 255, 0.05);
}

.friend-card-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 12px;
}

.friend-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  flex-shrink: 0;
}

.friend-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.friend-info {
  flex: 1;
}

.friend-nickname {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.friend-wxid {
  font-size: 14px;
  color: var(--text-secondary);
  font-family: 'Courier New', monospace;
}

.friend-status {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
}

.friend-status-badge {
  padding: 4px 8px;
  border-radius: var(--radius-small);
  font-size: 12px;
  font-weight: 500;
}

.friend-status-badge.friend {
  background: rgba(82, 196, 26, 0.1);
  color: var(--success-color);
}

.friend-status-badge.stranger {
  background: rgba(250, 173, 20, 0.1);
  color: var(--warning-color);
}

.friend-status-badge.not-found {
  background: rgba(245, 34, 45, 0.1);
  color: var(--error-color);
}

.friend-actions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
}

.friend-action-btn {
  padding: 8px 16px;
  border: none;
  border-radius: var(--radius-small);
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.friend-action-btn.primary {
  background: var(--primary-color);
  color: white;
}

.friend-action-btn.primary:hover {
  background: var(--primary-hover);
}

.friend-action-btn.secondary {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--border-light);
}

.friend-action-btn.secondary:hover {
  background: var(--bg-primary);
  color: var(--text-primary);
}

/* 发送消息区域 */
.send-message-section {
  background: var(--bg-primary);
  border-radius: var(--radius-large);
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--border-light);
}

.message-form-container {
  max-width: 600px;
  margin: 0 auto;
}

.selected-friend-info {
  background: rgba(24, 144, 255, 0.05);
  border: 1px solid rgba(24, 144, 255, 0.2);
  border-radius: var(--radius-medium);
  padding: 16px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.selected-friend-info .friend-avatar {
  width: 40px;
  height: 40px;
  font-size: 16px;
}

.selected-friend-info .friend-info .friend-nickname {
  font-size: 14px;
  margin-bottom: 2px;
}

.selected-friend-info .friend-info .friend-wxid {
  font-size: 12px;
}

.message-form textarea {
  resize: vertical;
  min-height: 100px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-tertiary);
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state h3 {
  margin-bottom: 8px;
  color: var(--text-secondary);
}

.empty-state p {
  font-size: 14px;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-secondary);
}

.loading-state i {
  font-size: 32px;
  margin-bottom: 16px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .friends-header h1 {
    font-size: 2rem;
  }
  
  .search-friends-section,
  .search-results-section,
  .send-message-section {
    padding: 16px;
    margin-bottom: 16px;
  }
  
  .friend-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .friend-actions {
    flex-wrap: wrap;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .form-actions .btn {
    width: 100%;
  }
}
